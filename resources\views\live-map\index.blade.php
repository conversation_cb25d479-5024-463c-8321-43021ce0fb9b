
@extends('layouts.app')

@section('title', 'Live Map - Delivery Tracking')

@push('styles')
<style>
    #map {
        height: 100%;
        width: 100%;
    }
    .gm-style-iw {
        max-width: 300px;
    }
    .custom-marker {
        background: white;
        border-radius: 50%;
        border: 3px solid;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        font-weight: bold;
        color: white;
    }
    .pickup-marker {
        border-color: #10B981;
        background-color: #10B981;
    }
    .delivery-marker {
        border-color: #EF4444;
        background-color: #EF4444;
    }
    .vehicle-marker {
        border-color: #3B82F6;
        background-color: #3B82F6;
    }
</style>
@endpush

@section('content')
<div class="min-h-screen bg-gray-50">
    
    <!-- Header -->
    <div class="bg-white shadow-sm">
        <div class="container mx-auto px-6 py-6">
            <div class="flex flex-col lg:flex-row justify-between items-start lg:items-center">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Live Delivery Map</h1>
                    <p class="text-gray-600 mt-1">Real-time tracking of all active deliveries</p>
                </div>
                
                <!-- Controls -->
                <div class="mt-4 lg:mt-0 flex flex-wrap gap-4">
                    <!-- Search -->
                    <div class="relative">
                        <input type="text" id="searchInput" placeholder="Search bookings, addresses..."
                               class="w-64 pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-search text-gray-400"></i>
                        </div>
                    </div>
                    
                    <!-- Status Filter -->
                    <select id="statusFilter" class="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500">
                        <option value="active">Active Deliveries</option>
                        <option value="all">All Deliveries</option>
                        <option value="pending">Pending Assignment</option>
                        <option value="confirmed">Confirmed</option>
                        <option value="delivery_enroute">Delivery Enroute</option>
                        <option value="in_progress">In Progress</option>
                        <option value="completed">Completed</option>
                        <option value="cancelled">Cancelled</option>
                    </select>
                    
                    <!-- Refresh Button -->
                    <button onclick="refreshMap()" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                        <i class="fas fa-sync-alt mr-2"></i>Refresh
                    </button>
                    
                    <!-- Auto-refresh Toggle -->
                    <label class="flex items-center">
                        <input type="checkbox" id="autoRefresh" checked class="mr-2">
                        <span class="text-sm text-gray-700">Auto-refresh</span>
                    </label>
                </div>
            </div>
        </div>
    </div>

    <div class="container mx-auto px-6 py-6">
        
        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <div class="bg-white rounded-xl shadow-sm p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-orange-100">
                        <i class="fas fa-truck text-orange-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Active Deliveries</p>
                        <p class="text-2xl font-bold text-gray-900" id="activeCount">{{ $stats['total_active'] }}</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-xl shadow-sm p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-yellow-100">
                        <i class="fas fa-clock text-yellow-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Pending Assignment</p>
                        <p class="text-2xl font-bold text-gray-900" id="pendingCount">{{ $stats['pending_assignment'] }}</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-xl shadow-sm p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-purple-100">
                        <i class="fas fa-route text-purple-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">In Transit</p>
                        <p class="text-2xl font-bold text-gray-900" id="transitCount">{{ $stats['in_transit'] }}</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-xl shadow-sm p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-green-100">
                        <i class="fas fa-check-circle text-green-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Completed Today</p>
                        <p class="text-2xl font-bold text-gray-900" id="completedCount">{{ $stats['completed_today'] }}</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            
            <!-- Map Container -->
            <div class="lg:col-span-2">
                <div class="bg-white rounded-xl shadow-sm">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex justify-between items-center">
                            <h3 class="text-lg font-bold text-gray-900">Live Delivery Map</h3>
                            <div class="flex space-x-2">
                                <button onclick="toggleDeliveries()" id="deliveriesToggle" class="px-3 py-1 text-xs bg-green-100 text-green-800 rounded-full">
                                    <i class="fas fa-box mr-1"></i>Hide Deliveries
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="p-0">
                        <div id="map" style="height: 600px; width: 100%;" class="flex items-center justify-center bg-gray-200">
                            <div id="map-placeholder">
                                <i class="fas fa-spinner fa-spin text-4xl text-gray-500"></i>
                                <p class="mt-4 text-gray-600">Loading map...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Delivery List -->
            <div class="space-y-6">
                
                <!-- Active Deliveries Summary -->
                <div class="bg-white rounded-xl shadow-sm">
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-bold text-gray-900">Delivery Overview</h3>
                        <p class="text-sm text-gray-600">{{ $bookings->count() }} active deliveries</p>
                    </div>
                    <div class="p-6">
                        <div class="grid grid-cols-2 gap-4">
                            <div class="text-center">
                                <div class="text-2xl font-bold text-blue-600">{{ $bookings->where('status', 'confirmed')->count() }}</div>
                                <div class="text-sm text-gray-500">Confirmed</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-purple-600">{{ $bookings->where('status', 'in_progress')->count() }}</div>
                                <div class="text-sm text-gray-500">In Progress</div>
                            </div>
                        </div>
                    </div>

                
                <!-- Active Deliveries List -->
                <div class="bg-white rounded-xl shadow-sm">
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-bold text-gray-900">Active Deliveries</h3>
                        <p class="text-sm text-gray-600" id="deliveryCount">{{ count($bookings) }} deliveries</p>
                    </div>
                    <div class="max-h-96 overflow-y-auto" id="deliveryList">
                        @forelse($bookings as $booking)
                            <div class="p-4 border-b border-gray-100 last:border-b-0 hover:bg-gray-50 cursor-pointer delivery-item" 
                                 data-booking-id="{{ $booking->id }}"
                                 onclick="focusOnDelivery({{ $booking->id }})">
                                <div class="flex items-start justify-between">
                                    <div class="flex-1">
                                        <div class="flex items-center mb-2">
                                            <span class="text-sm font-medium text-gray-900">{{ $booking->booking_id }}</span>
                                            <span class="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                                @switch($booking->status)
                                                    @case('confirmed')
                                                        bg-blue-100 text-blue-800
                                                        @break

                                                    @case('in_progress')
                                                        bg-purple-100 text-purple-800
                                                        @break
                                                    @default
                                                        bg-gray-100 text-gray-800
                                                @endswitch
                                            ">
                                                {{ ucfirst(str_replace('_', ' ', $booking->status)) }}
                                            </span>
                                        </div>
                                        <div class="space-y-1">
                                            <div class="flex items-center text-xs text-gray-600">
                                                <i class="fas fa-map-marker-alt text-green-500 mr-2"></i>
                                                <span class="truncate">{{ Str::limit($booking->pickup_address, 30) }}</span>
                                            </div>
                                            <div class="flex items-center text-xs text-gray-600">
                                                <i class="fas fa-map-marker-alt text-red-500 mr-2"></i>
                                                <span class="truncate">{{ Str::limit($booking->delivery_address, 30) }}</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="ml-2">
                                        <button onclick="event.stopPropagation(); viewBookingDetails({{ $booking->id }})" 
                                                class="text-orange-600 hover:text-orange-800 text-xs">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        @empty
                            <div class="p-6 text-center text-gray-500">
                                <i class="fas fa-box-open text-2xl mb-2"></i>
                                <p>No active deliveries</p>
                            </div>
                        @endforelse
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Booking Details Modal -->
<div id="bookingModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-2xl shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900" id="modalTitle">Booking Details</h3>
                <button onclick="closeBookingModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div id="modalContent">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>
</div>

@endsection

@push('scripts')
<!-- Google Maps JavaScript API -->
<script async defer src="https://maps.googleapis.com/maps/api/js?key={{ config('services.google_maps.api_key') }}&libraries=places&callback=initGoogleMaps"></script>

<script>
// Global variables
let map;
let markers = {
    deliveries: [],
    branches: []
};
let showDeliveries = true;
let showBranches = true;
let autoRefreshInterval;
let directionsService;
let directionsRenderer;

// Initialize Google Maps when API is loaded
function initGoogleMaps() {
    console.log('Google Maps API loaded');
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeLiveMap);
    } else {
        initializeLiveMap();
    }
}

function initializeLiveMap() {
    initializeMap();
    setupEventListeners();
    startAutoRefresh();
}

// Make initializeMap globally accessible
window.initializeMap = initializeMap;

// Initialize Google Maps
function initializeMap() {
    // Default center (Accra, Ghana)
    const defaultCenter = { lat: 5.6037, lng: -0.1870 };

    // Initialize the map
    map = new google.maps.Map(document.getElementById('map'), {
        center: defaultCenter,
        zoom: 12,
        mapTypeId: google.maps.MapTypeId.ROADMAP,
        styles: [
            {
                featureType: 'poi',
                elementType: 'labels',
                stylers: [{ visibility: 'off' }]
            }
        ]
    });

    // Initialize directions service
    directionsService = new google.maps.DirectionsService();
    directionsRenderer = new google.maps.DirectionsRenderer({
        suppressMarkers: true,
        polylineOptions: {
            strokeColor: '#F59E0B',
            strokeWeight: 4
        }
    });
    directionsRenderer.setMap(map);

    // Add custom controls
    addMapControls();

    // Load map data
    loadMapData();
    loadBranchData();
}

// Load all map data
function loadMapData() {
    const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

    fetch('/api/live-map/deliveries', {
        headers: {
            'X-CSRF-TOKEN': csrfToken,
            'Accept': 'application/json'
        }
    })
        .then(response => {
            if (response.status === 401) {
                throw new Error('Authentication failed. Please log in.');
            }
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(deliveries => {
            document.getElementById('map-placeholder').style.display = 'none';
            clearMarkers();
            addDeliveryMarkers(deliveries);
        }).catch(error => {
            console.error('Error loading map data:', error);
            const mapDiv = document.getElementById('map');
            mapDiv.innerHTML = `
                <div class="text-center text-red-500">
                    <i class="fas fa-exclamation-triangle text-4xl"></i>
                    <p class="mt-4">Failed to load map data.</p>
                    <p class="text-sm text-gray-600">${error.message}</p>
                </div>
            `;
        });
}

// Add delivery markers to map
function addDeliveryMarkers(deliveries) {
    deliveries.forEach(delivery => {
        // Create pickup marker
        const pickupMarker = new google.maps.Marker({
            position: { lat: delivery.pickup.latitude, lng: delivery.pickup.longitude },
            map: map,
            title: `Pickup: ${delivery.pickup.address}`,
            icon: {
                url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                    <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="12" cy="12" r="10" fill="#10B981" stroke="white" stroke-width="2"/>
                        <path d="M8 12l2 2 4-4" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                `),
                scaledSize: new google.maps.Size(32, 32)
            }
        });

        // Create delivery marker
        const deliveryMarker = new google.maps.Marker({
            position: { lat: delivery.delivery.latitude, lng: delivery.delivery.longitude },
            map: map,
            title: `Delivery: ${delivery.delivery.address}`,
            icon: {
                url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                    <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="12" cy="12" r="10" fill="#EF4444" stroke="white" stroke-width="2"/>
                        <path d="M12 8v4l3 3" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                `),
                scaledSize: new google.maps.Size(32, 32)
            }
        });

        // Create info windows
        const pickupInfoWindow = new google.maps.InfoWindow({
            content: `
                <div class="p-2">
                    <h4 class="font-bold">${delivery.booking_id} - Pickup</h4>
                    <p class="text-sm">${delivery.pickup.address}</p>
                    <p class="text-xs text-gray-600">Status: ${delivery.status}</p>
                    <button onclick="viewBookingDetails(${delivery.id})" class="mt-2 px-3 py-1 bg-orange-500 text-white text-xs rounded hover:bg-orange-600">
                        View Details
                    </button>
                </div>
            `
        });

        const deliveryInfoWindow = new google.maps.InfoWindow({
            content: `
                <div class="p-2">
                    <h4 class="font-bold">${delivery.booking_id} - Delivery</h4>
                    <p class="text-sm">${delivery.delivery.address}</p>
                    <p class="text-xs text-gray-600">Status: ${delivery.status}</p>
                    <button onclick="viewBookingDetails(${delivery.id})" class="mt-2 px-3 py-1 bg-orange-500 text-white text-xs rounded hover:bg-orange-600">
                        View Details
                    </button>
                </div>
            `
        });

        // Add click listeners for info windows
        pickupMarker.addListener('click', function() {
            pickupInfoWindow.open(map, pickupMarker);
        });

        deliveryMarker.addListener('click', function() {
            deliveryInfoWindow.open(map, deliveryMarker);
        });

        // Store markers for later reference
        markers.deliveries.push(pickupMarker, deliveryMarker);

        // Draw route between pickup and delivery
        if (delivery.status === 'in_progress' || delivery.status === 'confirmed') {
            drawRoute(delivery.pickup, delivery.delivery, delivery.booking_id);
        }
    });
}

// Draw route between pickup and delivery
function drawRoute(pickup, delivery, bookingId) {
    directionsService.route({
        origin: { lat: pickup.latitude, lng: pickup.longitude },
        destination: { lat: delivery.latitude, lng: delivery.longitude },
        travelMode: google.maps.TravelMode.DRIVING
    }, function(result, status) {
        if (status === 'OK') {
            // Create a new renderer for this route
            const routeRenderer = new google.maps.DirectionsRenderer({
                suppressMarkers: true,
                polylineOptions: {
                    strokeColor: '#F59E0B',
                    strokeWeight: 3,
                    strokeOpacity: 0.7
                }
            });
            routeRenderer.setMap(map);
            routeRenderer.setDirections(result);

            // Store the renderer for cleanup
            markers.deliveries.push(routeRenderer);
        }
    });
}

// Clear all markers
function clearMarkers() {
    markers.deliveries.forEach(marker => {
        if (marker.setMap) {
            marker.setMap(null);
        } else if (marker.setDirections) {
            // This is a directions renderer
            marker.setMap(null);
        }
    });
    markers.deliveries = [];

    markers.branches.forEach(marker => {
        if (marker.setMap) {
            marker.setMap(null);
        }
    });
    markers.branches = [];
}



// Toggle deliveries visibility
function toggleDeliveries() {
    showDeliveries = !showDeliveries;
    markers.deliveries.forEach(marker => {
        if (marker.setMap) {
            marker.setMap(showDeliveries ? map : null);
        }
    });

    const button = document.getElementById('deliveriesToggle');
    button.innerHTML = showDeliveries ?
        '<i class="fas fa-box mr-1"></i>Hide Deliveries' :
        '<i class="fas fa-box mr-1"></i>Show Deliveries';
    button.className = showDeliveries ?
        'px-3 py-1 text-xs bg-green-100 text-green-800 rounded-full' :
        'px-3 py-1 text-xs bg-gray-100 text-gray-800 rounded-full';
}

// Focus on specific delivery
function focusOnDelivery(bookingId) {
    // This would focus the map on the specific delivery
    // Implementation depends on having the booking data available
    console.log('Focus on delivery:', bookingId);
}


// View booking details
function viewBookingDetails(bookingId) {
    const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

    fetch(`/api/live-map/booking/${bookingId}`, {
        headers: {
            'X-CSRF-TOKEN': csrfToken,
            'Accept': 'application/json'
        }
    })
        .then(response => response.json())
        .then(data => {
            document.getElementById('modalTitle').textContent = `Booking ${data.booking_id}`;
            document.getElementById('modalContent').innerHTML = `
                <div class="space-y-4">
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <h4 class="font-medium text-gray-900">Pickup</h4>
                            <p class="text-sm text-gray-600">${data.pickup.address}</p>
                            <p class="text-xs text-gray-500">${data.pickup.person_name} - ${data.pickup.person_phone}</p>
                        </div>
                        <div>
                            <h4 class="font-medium text-gray-900">Delivery</h4>
                            <p class="text-sm text-gray-600">${data.delivery.address}</p>
                            <p class="text-xs text-gray-500">${data.delivery.receiver_name} - ${data.delivery.receiver_phone}</p>
                        </div>
                    </div>

                    <div class="grid grid-cols-3 gap-4 text-center">
                        <div>
                            <p class="text-xs text-gray-500">Status</p>
                            <p class="font-medium">${data.status}</p>
                        </div>
                        <div>
                            <p class="text-xs text-gray-500">Cost</p>
                            <p class="font-medium">CF$ ${data.estimated_cost}</p>
                        </div>
                        <div>
                            <p class="text-xs text-gray-500">Distance</p>
                            <p class="font-medium">${data.distance_km} km</p>
                        </div>
                    </div>
                </div>
            `;
            document.getElementById('bookingModal').classList.remove('hidden');
        })
        .catch(error => {
            console.error('Error loading booking details:', error);
        });
}

// Close booking modal
function closeBookingModal() {
    document.getElementById('bookingModal').classList.add('hidden');
}

// Refresh map data
function refreshMap() {
    loadMapData();
    // Refresh statistics
    location.reload();
}

// Setup event listeners
function setupEventListeners() {
    // Search functionality
    document.getElementById('searchInput').addEventListener('input', function(e) {
        const searchTerm = e.target.value.toLowerCase();
        const deliveryItems = document.querySelectorAll('.delivery-item');
        
        deliveryItems.forEach(item => {
            const text = item.textContent.toLowerCase();
            if (text.includes(searchTerm)) {
                item.style.display = '';
            } else {
                item.style.display = 'none';
            }
        });
    });
    
    // Status filter
    document.getElementById('statusFilter').addEventListener('change', function(e) {
        const status = e.target.value;
        const url = new URL(window.location);
        url.searchParams.set('status', status);
        window.location.href = url.toString();
    });
    
    // Auto-refresh toggle
    document.getElementById('autoRefresh').addEventListener('change', function(e) {
        if (e.target.checked) {
            startAutoRefresh();
        } else {
            stopAutoRefresh();
        }
    });
}

// Auto-refresh functionality
function startAutoRefresh() {
    autoRefreshInterval = setInterval(() => {
        loadMapData();
    }, 30000); // Refresh every 30 seconds
}

function stopAutoRefresh() {
    if (autoRefreshInterval) {
        clearInterval(autoRefreshInterval);
    }
}

// Close modal when clicking outside
document.getElementById('bookingModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeBookingModal();
    }
});
</script>

// Load branch data and add branch markers
function loadBranchData() {
    const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

    fetch('/api/live-map/branches', {
        headers: {
            'X-CSRF-TOKEN': csrfToken,
            'Accept': 'application/json'
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(branches => {
        addBranchMarkers(branches);
    })
    .catch(error => {
        console.error('Error loading branch data:', error);
    });
}

// Add branch markers to map
function addBranchMarkers(branches) {
    branches.forEach(branch => {
        const branchMarker = new google.maps.Marker({
            position: { lat: branch.latitude, lng: branch.longitude },
            map: showBranches && branch.status ? map : null,
            title: branch.name,
            icon: {
                url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                    <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="12" cy="12" r="10" fill="#3B82F6" stroke="white" stroke-width="2"/>
                        <path d="M3 21h18M5 21V7l8-4v18M19 21V10l-6-3" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                `),
                scaledSize: new google.maps.Size(32, 32)
            }
        });

        const branchInfoWindow = new google.maps.InfoWindow({
            content: `
                <div class="p-3 min-w-[200px]">
                    <h4 class="font-bold text-blue-600">${branch.name}</h4>
                    <p class="text-sm text-gray-700 mt-1">${branch.address}</p>
                    ${branch.phone ? `<p class="text-xs text-gray-600 mt-1"><i class="fas fa-phone mr-1"></i>${branch.phone}</p>` : ''}
                    ${branch.operating_hours ? `<p class="text-xs text-gray-600 mt-1"><i class="fas fa-clock mr-1"></i>${branch.formatted_operating_hours || 'See details'}</p>` : ''}
                    <div class="mt-2">
                        <span class="px-2 py-1 text-xs rounded-full ${branch.status ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                            ${branch.status ? 'Active' : 'Inactive'}
                        </span>
                    </div>
                </div>
            `
        });

        branchMarker.addListener('click', function() {
            branchInfoWindow.open(map, branchMarker);
        });

        markers.branches.push(branchMarker);
    });
}

// Add custom map controls
function addMapControls() {
    // Create custom control div
    const controlDiv = document.createElement('div');
    controlDiv.style.backgroundColor = 'white';
    controlDiv.style.border = '2px solid #fff';
    controlDiv.style.borderRadius = '3px';
    controlDiv.style.boxShadow = '0 2px 6px rgba(0,0,0,.3)';
    controlDiv.style.cursor = 'pointer';
    controlDiv.style.marginBottom = '22px';
    controlDiv.style.textAlign = 'center';
    controlDiv.style.padding = '5px';
    controlDiv.innerHTML = `
        <button id="toggle-branches" onclick="toggleBranches()" class="block w-full px-3 py-1 text-xs bg-blue-100 text-blue-800 rounded mb-1">
            <i class="fas fa-building mr-1"></i>Hide Branches
        </button>
        <button id="show-routes" onclick="showDeliveryRoutes()" class="block w-full px-3 py-1 text-xs bg-purple-100 text-purple-800 rounded">
            <i class="fas fa-route mr-1"></i>Show Routes
        </button>
    `;

    // Position the control on the map
    map.controls[google.maps.ControlPosition.TOP_RIGHT].push(controlDiv);
}

// Toggle branch markers
function toggleBranches() {
    showBranches = !showBranches;
    const button = document.getElementById('toggle-branches');

    markers.branches.forEach(marker => {
        marker.setMap(showBranches ? map : null);
    });

    button.innerHTML = showBranches ?
        '<i class="fas fa-building mr-1"></i>Hide Branches' :
        '<i class="fas fa-building mr-1"></i>Show Branches';
    button.className = showBranches ?
        'block w-full px-3 py-1 text-xs bg-blue-100 text-blue-800 rounded mb-1' :
        'block w-full px-3 py-1 text-xs bg-gray-100 text-gray-800 rounded mb-1';
}

// Show delivery routes (placeholder for route visualization)
function showDeliveryRoutes() {
    // This will be implemented in the enhanced delivery tracking phase
    console.log('Show delivery routes - to be implemented');
}

// Fallback initialization if Google Maps fails to load
document.addEventListener('DOMContentLoaded', function() {
    // Check if Google Maps loaded, if not show error
    setTimeout(() => {
        if (typeof google === 'undefined' || !google.maps) {
            console.error('Google Maps API failed to load');
            const mapDiv = document.getElementById('map');
            mapDiv.innerHTML = `
                <div class="flex items-center justify-center h-full text-center text-red-500">
                    <div>
                        <i class="fas fa-exclamation-triangle text-4xl mb-4"></i>
                        <p class="text-lg font-semibold">Failed to load Google Maps</p>
                        <p class="text-sm text-gray-600">Please check your internet connection and refresh the page</p>
                        <button onclick="location.reload()" class="mt-4 px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600">
                            Refresh Page
                        </button>
                    </div>
                </div>
            `;
        }
    }, 5000);
});

</script>
@endpush

